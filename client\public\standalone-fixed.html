<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Kế toán TT 133 - Fixed Version</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 95%;
            max-width: 1200px;
            min-height: 80vh;
            display: grid;
            grid-template-columns: 280px 1fr;
            position: relative;
        }

        .sidebar {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            grid-column: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logo-icon {
            background: rgba(255,255,255,0.1);
            padding: 0.75rem;
            border-radius: 12px;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .main-content {
            padding: 2rem;
            background: #f8f9fa;
            grid-column: 2;
            overflow-x: auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid;
        }

        .stat-card.income { border-left-color: #27ae60; }
        .stat-card.expense { border-left-color: #e74c3c; }
        .stat-card.profit { border-left-color: #3498db; }
        .stat-card.transactions { border-left-color: #f39c12; }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .content-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .transaction-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input, .form-group select, .form-group textarea {
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }

        .transaction-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .amount-positive {
            color: #27ae60;
            font-weight: 600;
        }

        .amount-negative {
            color: #e74c3c;
            font-weight: 600;
        }

        .hidden {
            display: none;
        }

        /* Transaction Cards */
        .transaction-categories {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .transaction-card {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .transaction-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .card-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            width: 40px;
            text-align: center;
        }

        .card-content {
            flex: 1;
        }

        .card-content h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }

        .card-content p {
            margin: 0;
            font-size: 0.875rem;
            color: #666;
            line-height: 1.3;
        }

        .card-arrow {
            color: #999;
            font-size: 1rem;
            transition: color 0.2s ease;
        }

        .transaction-card:hover .card-arrow {
            color: #007bff;
        }

        /* Wizard Modal Styles */
        .wizard-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .wizard-modal.show {
            display: flex;
        }

        .wizard-content {
            position: relative;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            z-index: 1001;
        }

        .wizard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #eee;
        }

        .wizard-header h2 {
            margin: 0;
            color: #333;
            font-size: 1.5rem;
        }

        .wizard-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
            padding: 0.5rem;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .wizard-close:hover {
            background: #f5f5f5;
            color: #333;
        }

        .wizard-body {
            padding: 2rem;
        }

        .wizard-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-top: 1px solid #eee;
            background: #f8f9fa;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }

            .sidebar {
                grid-column: 1;
                grid-row: 1;
                padding: 1rem;
            }

            .main-content {
                grid-column: 1;
                grid-row: 2;
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            /* Mobile responsive for transaction grid */
            div[style*="grid-template-columns: 1fr 1fr"] {
                grid-template-columns: 1fr !important;
            }

            .transaction-card {
                padding: 0.75rem;
            }

            .card-content h4 {
                font-size: 0.9rem;
            }

            .card-content p {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <div class="logo-icon">
                    <span class="material-icons">trending_up</span>
                </div>
                <div>
                    <h3>Kế toán TT 133</h3>
                    <small>Phiên bản Fixed</small>
                </div>
            </div>

            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                            <span class="material-icons">dashboard</span>
                            Tổng quan
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('transactions')">
                            <span class="material-icons">receipt</span>
                            Giao dịch
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('reports')">
                            <span class="material-icons">assessment</span>
                            Báo cáo
                        </a>
                    </li>
                </ul>
            </nav>

            <div style="margin-top: auto; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1);">
                <small>© 2024 App Kế toán TT 133</small>
                <br>
                <small style="opacity: 0.7;">Phiên bản Fixed - Layout ổn định</small>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section">
                <div class="header">
                    <h1>Dashboard</h1>
                    <button class="btn" onclick="showSection('transactions')">
                        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">add</span>
                        Thêm giao dịch
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-card income">
                        <div class="stat-value" id="totalIncome">0 ₫</div>
                        <div class="stat-label">Tổng thu tháng này</div>
                    </div>
                    <div class="stat-card expense">
                        <div class="stat-value" id="totalExpense">0 ₫</div>
                        <div class="stat-label">Tổng chi tháng này</div>
                    </div>
                    <div class="stat-card profit">
                        <div class="stat-value" id="netProfit">0 ₫</div>
                        <div class="stat-label">Lợi nhuận ròng</div>
                    </div>
                    <div class="stat-card transactions">
                        <div class="stat-value" id="transactionCount">0</div>
                        <div class="stat-label">Số giao dịch</div>
                    </div>
                </div>

                <div class="content-section">
                    <h3>Giao dịch gần đây</h3>
                    <div id="recentTransactions" class="transaction-list">
                        <p style="text-align: center; color: #666; padding: 2rem;">
                            Chưa có giao dịch nào. Hãy thêm giao dịch đầu tiên!
                        </p>
                    </div>
                </div>
            </div>

            <!-- Transactions Section -->
            <div id="transactions" class="section hidden">
                <div class="header">
                    <h1>Quản lý giao dịch</h1>
                    <p style="color: #666; margin-top: 0.5rem;">Chọn loại giao dịch và làm theo hướng dẫn wizard</p>
                </div>

                <!-- Manual Transaction Form -->
                <div class="content-section">
                    <h3>📝 Nhập giao dịch thủ công</h3>
                    <form id="transactionForm">
                        <div class="transaction-form">
                            <div class="form-group">
                                <label>Loại giao dịch *</label>
                                <select id="transactionType" required>
                                    <option value="">Chọn loại</option>
                                    <option value="income">Thu nhập</option>
                                    <option value="expense">Chi phí</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Danh mục *</label>
                                <select id="category" required>
                                    <option value="">Chọn danh mục</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Mô tả *</label>
                                <input type="text" id="description" placeholder="Mô tả giao dịch" required>
                            </div>
                            <div class="form-group">
                                <label>Số tiền (VND) *</label>
                                <input type="number" id="amount" placeholder="0" min="1" required>
                            </div>
                            <div class="form-group">
                                <label>Ngày giao dịch *</label>
                                <input type="date" id="date" required>
                            </div>
                            <div class="form-group">
                                <label>Ghi chú</label>
                                <input type="text" id="notes" placeholder="Ghi chú thêm (tùy chọn)">
                            </div>
                        </div>
                        <button type="submit" class="btn">
                            <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">add</span>
                            Thêm giao dịch
                        </button>
                    </form>
                </div>

                <!-- Transaction Categories Grid -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">

                    <!-- Income Section -->
                    <div class="content-section" style="background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%); border: 2px solid #27ae60; border-radius: 12px;">
                        <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                            <span class="material-icons" style="color: #27ae60; font-size: 2rem; margin-right: 0.5rem;">trending_up</span>
                            <h3 style="color: #27ae60; margin: 0;">📈 Thu nhập</h3>
                        </div>

                        <div class="transaction-categories">
                            <!-- Service Revenue -->
                            <div class="transaction-card" onclick="openWizard('income', 'service_revenue')">
                                <div class="card-icon">💼</div>
                                <div class="card-content">
                                    <h4>Thu cung cấp dịch vụ</h4>
                                    <p>Tư vấn, thiết kế, phát triển, bảo trì</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Product Sales -->
                            <div class="transaction-card" onclick="openWizard('income', 'product_sales')">
                                <div class="card-icon">🛒</div>
                                <div class="card-content">
                                    <h4>Thu bán hàng hóa</h4>
                                    <p>Bán sản phẩm, thiết bị, hàng hóa</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Other Income -->
                            <div class="transaction-card" onclick="openWizard('income', 'other_income')">
                                <div class="card-icon">➕</div>
                                <div class="card-content">
                                    <h4>Thu khác</h4>
                                    <p>Các khoản thu khác</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>
                        </div>
                    </div>

                    <!-- Expense Section -->
                    <div class="content-section" style="background: linear-gradient(135deg, #ffeaea 0%, #fff0f0 100%); border: 2px solid #e74c3c; border-radius: 12px;">
                        <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                            <span class="material-icons" style="color: #e74c3c; font-size: 2rem; margin-right: 0.5rem;">trending_down</span>
                            <h3 style="color: #e74c3c; margin: 0;">📉 Chi phí</h3>
                        </div>

                        <div class="transaction-categories">
                            <!-- Salary Expense -->
                            <div class="transaction-card" onclick="openWizard('expense', 'salary_expense')">
                                <div class="card-icon">👥</div>
                                <div class="card-content">
                                    <h4>Chi lương</h4>
                                    <p>Lương, thưởng, phụ cấp nhân viên</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Tax Payment -->
                            <div class="transaction-card" onclick="openWizard('expense', 'tax_payment')">
                                <div class="card-icon">🏛️</div>
                                <div class="card-content">
                                    <h4>Chi thuế</h4>
                                    <p>GTGT, TNDN, TNCN, môn bài</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Other Expense -->
                            <div class="transaction-card" onclick="openWizard('expense', 'other_expense')">
                                <div class="card-icon">➖</div>
                                <div class="card-content">
                                    <h4>Chi khác</h4>
                                    <p>Các khoản chi khác</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction History -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <!-- Income History -->
                    <div class="content-section">
                        <h3 style="color: #27ae60;">📈 Lịch sử thu nhập</h3>
                        <div id="incomeHistory" class="transaction-list">
                            <p style="text-align: center; color: #666; padding: 2rem;">
                                Chưa có giao dịch thu nào
                            </p>
                        </div>
                    </div>

                    <!-- Expense History -->
                    <div class="content-section">
                        <h3 style="color: #e74c3c;">📉 Lịch sử chi phí</h3>
                        <div id="expenseHistory" class="transaction-list">
                            <p style="text-align: center; color: #666; padding: 2rem;">
                                Chưa có giao dịch chi nào
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports Section -->
            <div id="reports" class="section hidden">
                <div class="header">
                    <h1>Báo cáo tài chính</h1>
                </div>

                <div class="content-section">
                    <h3>Báo cáo tháng này</h3>
                    <div id="monthlyReport">
                        <p>Báo cáo sẽ được tạo tự động dựa trên dữ liệu giao dịch</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wizard Modal -->
    <div id="wizardModal" class="wizard-modal">
        <div class="wizard-content">
            <div class="wizard-header">
                <h2 id="wizardTitle">Wizard Giao dịch</h2>
                <button class="wizard-close" onclick="closeWizard()">×</button>
            </div>
            <div class="wizard-body">
                <div id="wizardContent">
                    <h3>Thông tin giao dịch</h3>
                    <div class="transaction-form">
                        <div class="form-group">
                            <label>Mô tả *</label>
                            <input type="text" id="wizardDescription" placeholder="Nhập mô tả" required>
                        </div>
                        <div class="form-group">
                            <label>Số tiền (VND) *</label>
                            <input type="number" id="wizardAmount" placeholder="0" min="1" required>
                        </div>
                        <div class="form-group">
                            <label>Ngày giao dịch *</label>
                            <input type="date" id="wizardDate" required>
                        </div>
                        <div class="form-group">
                            <label>Ghi chú</label>
                            <textarea id="wizardNotes" placeholder="Ghi chú thêm" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="wizard-footer">
                <button class="btn" onclick="closeWizard()">Hủy</button>
                <button class="btn btn-success" onclick="saveWizardTransaction()">Lưu giao dịch</button>
            </div>
        </div>
    </div>

    <script>
        // Simple app state
        let transactions = JSON.parse(localStorage.getItem('transactions') || '[]');

        const categories = {
            income: [
                'Doanh thu bán hàng',
                'Doanh thu dịch vụ',
                'Thu nhập khác',
                'Lãi tiền gửi'
            ],
            expense: [
                'Chi phí nguyên vật liệu',
                'Chi phí nhân công',
                'Chi phí văn phòng',
                'Chi phí thuế',
                'Chi phí khác'
            ]
        };

        let currentWizardData = {};

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            setDefaultDate();
            updateCategories();
            updateDashboard();

            // Setup form submission
            document.getElementById('transactionForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addTransaction();
            });

            // Setup transaction type change
            document.getElementById('transactionType').addEventListener('change', updateCategories);
        });

        function setDefaultDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('date').value = today;
            document.getElementById('wizardDate').value = today;
        }

        function updateCategories() {
            const type = document.getElementById('transactionType').value;
            const categorySelect = document.getElementById('category');

            categorySelect.innerHTML = '<option value="">Chọn danh mục</option>';

            if (type && categories[type]) {
                categories[type].forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    categorySelect.appendChild(option);
                });
            }
        }

        function addTransaction() {
            const formData = {
                id: Date.now(),
                type: document.getElementById('transactionType').value,
                description: document.getElementById('description').value,
                amount: parseFloat(document.getElementById('amount').value),
                date: document.getElementById('date').value,
                category: document.getElementById('category').value,
                notes: document.getElementById('notes').value,
                createdAt: new Date().toISOString()
            };

            // Validation
            if (!formData.type || !formData.description || !formData.amount || !formData.date || !formData.category) {
                alert('Vui lòng điền đầy đủ thông tin bắt buộc!');
                return;
            }

            if (formData.amount <= 0) {
                alert('Số tiền phải lớn hơn 0!');
                return;
            }

            // Add transaction
            transactions.push(formData);
            saveTransactions();
            updateDashboard();
            clearForm();

            alert('✅ Đã thêm giao dịch thành công!');
            showSection('dashboard');
        }

        function clearForm() {
            document.getElementById('transactionForm').reset();
            setDefaultDate();
            updateCategories();
        }

        function saveTransactions() {
            localStorage.setItem('transactions', JSON.stringify(transactions));
        }

        function updateDashboard() {
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();

            // Filter transactions for current month
            const monthlyTransactions = transactions.filter(t => {
                const transactionDate = new Date(t.date);
                return transactionDate.getMonth() === currentMonth &&
                       transactionDate.getFullYear() === currentYear;
            });

            // Calculate totals
            const totalIncome = monthlyTransactions
                .filter(t => t.type === 'income')
                .reduce((sum, t) => sum + t.amount, 0);

            const totalExpense = monthlyTransactions
                .filter(t => t.type === 'expense')
                .reduce((sum, t) => sum + t.amount, 0);

            const netProfit = totalIncome - totalExpense;

            // Update dashboard stats
            document.getElementById('totalIncome').textContent = formatCurrency(totalIncome);
            document.getElementById('totalExpense').textContent = formatCurrency(totalExpense);
            document.getElementById('netProfit').textContent = formatCurrency(netProfit);
            document.getElementById('transactionCount').textContent = monthlyTransactions.length;

            // Update recent transactions
            updateRecentTransactions();
            updateIncomeHistory();
            updateExpenseHistory();
        }

        function updateRecentTransactions() {
            const container = document.getElementById('recentTransactions');
            const recentTransactions = transactions
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                .slice(0, 5);

            if (recentTransactions.length === 0) {
                container.innerHTML = `
                    <p style="text-align: center; color: #666; padding: 2rem;">
                        Chưa có giao dịch nào. Hãy thêm giao dịch đầu tiên!
                    </p>
                `;
                return;
            }

            container.innerHTML = recentTransactions.map(transaction => `
                <div class="transaction-item">
                    <div>
                        <div style="font-weight: 500;">${transaction.description}</div>
                        <div style="font-size: 0.9rem; color: #666;">
                            ${transaction.category} • ${formatDate(transaction.date)}
                        </div>
                    </div>
                    <div class="${transaction.type === 'income' ? 'amount-positive' : 'amount-negative'}">
                        ${transaction.type === 'income' ? '+' : '-'}${formatCurrency(transaction.amount)}
                    </div>
                </div>
            `).join('');
        }

        function updateIncomeHistory() {
            const container = document.getElementById('incomeHistory');
            if (!container) return;

            const incomeTransactions = transactions
                .filter(t => t.type === 'income')
                .sort((a, b) => new Date(b.date) - new Date(a.date));

            if (incomeTransactions.length === 0) {
                container.innerHTML = `
                    <p style="text-align: center; color: #666; padding: 2rem;">
                        Chưa có giao dịch thu nào
                    </p>
                `;
                return;
            }

            container.innerHTML = incomeTransactions.map(transaction => `
                <div class="transaction-item">
                    <div>
                        <div style="font-weight: 500;">${transaction.description}</div>
                        <div style="font-size: 0.9rem; color: #666;">
                            ${transaction.category} • ${formatDate(transaction.date)}
                            ${transaction.notes ? ` • ${transaction.notes}` : ''}
                        </div>
                    </div>
                    <div class="amount-positive">
                        +${formatCurrency(transaction.amount)}
                    </div>
                </div>
            `).join('');
        }

        function updateExpenseHistory() {
            const container = document.getElementById('expenseHistory');
            if (!container) return;

            const expenseTransactions = transactions
                .filter(t => t.type === 'expense')
                .sort((a, b) => new Date(b.date) - new Date(a.date));

            if (expenseTransactions.length === 0) {
                container.innerHTML = `
                    <p style="text-align: center; color: #666; padding: 2rem;">
                        Chưa có giao dịch chi nào
                    </p>
                `;
                return;
            }

            container.innerHTML = expenseTransactions.map(transaction => `
                <div class="transaction-item">
                    <div>
                        <div style="font-weight: 500;">${transaction.description}</div>
                        <div style="font-size: 0.9rem; color: #666;">
                            ${transaction.category} • ${formatDate(transaction.date)}
                            ${transaction.notes ? ` • ${transaction.notes}` : ''}
                        </div>
                    </div>
                    <div class="amount-negative">
                        -${formatCurrency(transaction.amount)}
                    </div>
                </div>
            `).join('');
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('vi-VN');
        }

        // Navigation functions
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');

            // Update navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            event.target.closest('.nav-link').classList.add('active');
        }

        // Wizard functions
        function openWizard(type, templateId) {
            console.log('Opening wizard:', type, templateId);

            currentWizardData = {
                type: type,
                templateId: templateId
            };

            const titles = {
                'service_revenue': '💼 Thu cung cấp dịch vụ',
                'product_sales': '🛒 Thu bán hàng hóa',
                'other_income': '➕ Thu khác',
                'salary_expense': '👥 Chi lương',
                'tax_payment': '🏛️ Chi thuế',
                'other_expense': '➖ Chi khác'
            };

            document.getElementById('wizardTitle').textContent = titles[templateId] || 'Wizard Giao dịch';
            document.getElementById('wizardModal').classList.add('show');

            // Clear wizard form
            document.getElementById('wizardDescription').value = '';
            document.getElementById('wizardAmount').value = '';
            document.getElementById('wizardNotes').value = '';
            setDefaultDate();
        }

        function closeWizard() {
            document.getElementById('wizardModal').classList.remove('show');
            currentWizardData = {};
        }

        function saveWizardTransaction() {
            const description = document.getElementById('wizardDescription').value.trim();
            const amount = parseFloat(document.getElementById('wizardAmount').value);
            const date = document.getElementById('wizardDate').value;
            const notes = document.getElementById('wizardNotes').value.trim();

            if (!description || !amount || !date) {
                alert('Vui lòng điền đầy đủ thông tin bắt buộc!');
                return;
            }

            if (amount <= 0) {
                alert('Số tiền phải lớn hơn 0!');
                return;
            }

            // Get category based on template
            const categoryMap = {
                'service_revenue': 'Doanh thu dịch vụ',
                'product_sales': 'Doanh thu bán hàng',
                'other_income': 'Thu nhập khác',
                'salary_expense': 'Chi phí nhân công',
                'tax_payment': 'Chi phí thuế',
                'other_expense': 'Chi phí khác'
            };

            const transactionData = {
                id: Date.now(),
                type: currentWizardData.type,
                description: description,
                amount: amount,
                date: date,
                category: categoryMap[currentWizardData.templateId] || 'Khác',
                notes: notes,
                createdAt: new Date().toISOString()
            };

            transactions.push(transactionData);
            saveTransactions();
            updateDashboard();
            closeWizard();

            alert('✅ Đã thêm giao dịch thành công!');
            showSection('transactions');
        }
    </script>
</body>
</html>