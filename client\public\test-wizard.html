<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Wizard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        
        /* Wizard Modal Styles */
        .wizard-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.5);
        }

        .wizard-content {
            position: relative;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .wizard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #eee;
        }

        .wizard-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
            padding: 0.5rem;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .wizard-body {
            padding: 2rem;
        }

        .wizard-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-top: 1px solid #eee;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>Test Wizard Functionality</h1>
    
    <button class="test-button" onclick="testWizard()">Test Wizard</button>
    <button class="test-button" onclick="testDirectModal()">Test Direct Modal</button>
    
    <div id="log" style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px;">
        <h3>Log:</h3>
        <div id="logContent"></div>
    </div>

    <script>
        function log(message) {
            const logContent = document.getElementById('logContent');
            logContent.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function testWizard() {
            log('Testing wizard...');
            try {
                if (typeof openTransactionWizard === 'function') {
                    openTransactionWizard('income', 'service_revenue');
                    log('Wizard function called successfully');
                } else {
                    log('ERROR: openTransactionWizard function not found');
                }
            } catch (error) {
                log('ERROR: ' + error.message);
            }
        }

        function testDirectModal() {
            log('Testing direct modal creation...');
            try {
                const modal = document.createElement('div');
                modal.id = 'testModal';
                modal.className = 'wizard-modal';
                modal.style.display = 'flex';
                modal.innerHTML = `
                    <div class="wizard-content">
                        <div class="wizard-header">
                            <h2>Test Modal</h2>
                            <button class="wizard-close" onclick="closeTestModal()">×</button>
                        </div>
                        <div class="wizard-body">
                            <p>This is a test modal to verify CSS and modal creation works.</p>
                        </div>
                        <div class="wizard-footer">
                            <button class="test-button" onclick="closeTestModal()">Close</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                log('Direct modal created successfully');
            } catch (error) {
                log('ERROR creating direct modal: ' + error.message);
            }
        }

        function closeTestModal() {
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.remove();
                log('Test modal closed');
            }
        }

        // Load the main app script
        const script = document.createElement('script');
        script.src = 'standalone-app.js';
        script.onload = function() {
            log('Main app script loaded');
        };
        script.onerror = function() {
            log('ERROR: Failed to load main app script');
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
