<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Kế toán TT 133 - Phiên bản Standalone</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 95%;
            max-width: 1200px;
            min-height: 80vh;
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: 1fr;
        }

        .sidebar {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            grid-column: 1;
            grid-row: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: #3498db;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .main-content {
            padding: 2rem;
            background: #f8f9fa;
            grid-column: 2;
            grid-row: 1;
            overflow-x: auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid;
        }

        .stat-card.income { border-left-color: #27ae60; }
        .stat-card.expense { border-left-color: #e74c3c; }
        .stat-card.profit { border-left-color: #3498db; }
        .stat-card.transactions { border-left-color: #f39c12; }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .content-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .transaction-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input, .form-group select {
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }

        .transaction-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .amount-positive {
            color: #27ae60;
            font-weight: 600;
        }

        .amount-negative {
            color: #e74c3c;
            font-weight: 600;
        }

        .hidden {
            display: none;
        }

        /* Transaction Cards */
        .transaction-categories {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .transaction-card {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .transaction-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .card-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            width: 40px;
            text-align: center;
        }

        .card-content {
            flex: 1;
        }

        .card-content h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }

        .card-content p {
            margin: 0;
            font-size: 0.875rem;
            color: #666;
            line-height: 1.3;
        }

        .card-arrow {
            color: #999;
            font-size: 1rem;
            transition: color 0.2s ease;
        }

        .transaction-card:hover .card-arrow {
            color: #007bff;
        }



        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }

            .sidebar {
                grid-column: 1;
                grid-row: 1;
                padding: 1rem;
            }

            .main-content {
                grid-column: 1;
                grid-row: 2;
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            /* Mobile responsive for transaction grid */
            div[style*="grid-template-columns: 1fr 1fr"] {
                grid-template-columns: 1fr !important;
            }

            .transaction-card {
                padding: 0.75rem;
            }

            .card-content h4 {
                font-size: 0.9rem;
            }

            .card-content p {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <div class="logo-icon">
                    <span class="material-icons">trending_up</span>
                </div>
                <div>
                    <h3>Kế toán TT 133</h3>
                    <small>Phiên bản Standalone</small>
                </div>
            </div>

            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                            <span class="material-icons">dashboard</span>
                            Tổng quan
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('transactions')">
                            <span class="material-icons">receipt</span>
                            Giao dịch
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('reports')">
                            <span class="material-icons">assessment</span>
                            Báo cáo
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('tax')">
                            <span class="material-icons">account_balance</span>
                            Quản lý thuế
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('google-sheets')">
                            <span class="material-icons">cloud_sync</span>
                            Google Sheets
                        </a>
                    </li>
                </ul>
            </nav>

            <div style="margin-top: auto; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1);">
                <small>© 2024 App Kế toán TT 133</small>
                <br>
                <small style="opacity: 0.7;">Phiên bản demo - Dữ liệu lưu trên trình duyệt</small>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section">
                <div class="header">
                    <h1>Dashboard</h1>
                    <button class="btn" onclick="showSection('transactions')">
                        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">add</span>
                        Thêm giao dịch
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-card income">
                        <div class="stat-value" id="totalIncome">0 ₫</div>
                        <div class="stat-label">Tổng thu tháng này</div>
                    </div>
                    <div class="stat-card expense">
                        <div class="stat-value" id="totalExpense">0 ₫</div>
                        <div class="stat-label">Tổng chi tháng này</div>
                    </div>
                    <div class="stat-card profit">
                        <div class="stat-value" id="netProfit">0 ₫</div>
                        <div class="stat-label">Lợi nhuận ròng</div>
                    </div>
                    <div class="stat-card transactions">
                        <div class="stat-value" id="transactionCount">0</div>
                        <div class="stat-label">Số giao dịch</div>
                    </div>
                </div>

                <div class="content-section">
                    <h3>Giao dịch gần đây</h3>
                    <div id="recentTransactions" class="transaction-list">
                        <p style="text-align: center; color: #666; padding: 2rem;">
                            Chưa có giao dịch nào. Hãy thêm giao dịch đầu tiên!
                        </p>
                    </div>
                </div>
            </div>

            <!-- Transactions Section -->
            <div id="transactions" class="section hidden">
                <div class="header">
                    <h1>Quản lý giao dịch</h1>
                    <p style="color: #666; margin-top: 0.5rem;">Chọn loại giao dịch và làm theo hướng dẫn wizard</p>
                </div>

                <!-- Manual Transaction Form -->
                <div class="content-section">
                    <h3>📝 Nhập giao dịch thủ công</h3>
                    <form id="transactionForm">
                        <div class="transaction-form">
                            <div class="form-group">
                                <label>Loại giao dịch *</label>
                                <select id="transactionType" required>
                                    <option value="">Chọn loại</option>
                                    <option value="income">Thu nhập</option>
                                    <option value="expense">Chi phí</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Danh mục *</label>
                                <select id="category" required>
                                    <option value="">Chọn danh mục</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Mô tả *</label>
                                <input type="text" id="description" placeholder="Mô tả giao dịch" required>
                            </div>
                            <div class="form-group">
                                <label>Số tiền (VND) *</label>
                                <input type="number" id="amount" placeholder="0" min="1" required>
                            </div>
                            <div class="form-group">
                                <label>Ngày giao dịch *</label>
                                <input type="date" id="date" required>
                            </div>
                            <div class="form-group">
                                <label>Ghi chú</label>
                                <input type="text" id="notes" placeholder="Ghi chú thêm (tùy chọn)">
                            </div>
                        </div>
                        <button type="submit" class="btn">
                            <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">add</span>
                            Thêm giao dịch
                        </button>
                    </form>
                </div>

                <!-- Transaction Categories Grid -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">

                    <!-- Income Section -->
                    <div class="content-section" style="background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%); border: 2px solid #27ae60; border-radius: 12px;">
                        <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                            <span class="material-icons" style="color: #27ae60; font-size: 2rem; margin-right: 0.5rem;">trending_up</span>
                            <h3 style="color: #27ae60; margin: 0;">📈 Thu nhập</h3>
                        </div>

                        <div class="transaction-categories">
                            <!-- Service Revenue -->
                            <div class="transaction-card" onclick="openTransactionWizard('income', 'service_revenue')">
                                <div class="card-icon">💼</div>
                                <div class="card-content">
                                    <h4>Thu cung cấp dịch vụ</h4>
                                    <p>Tư vấn, thiết kế, phát triển, bảo trì</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Product Sales -->
                            <div class="transaction-card" onclick="openTransactionWizard('income', 'product_sales')">
                                <div class="card-icon">🛒</div>
                                <div class="card-content">
                                    <h4>Thu bán hàng hóa</h4>
                                    <p>Bán sản phẩm, thiết bị, hàng hóa</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Interest Income -->
                            <div class="transaction-card" onclick="openTransactionWizard('income', 'interest_income')">
                                <div class="card-icon">💰</div>
                                <div class="card-content">
                                    <h4>Thu lãi tiền gửi</h4>
                                    <p>Lãi ngân hàng, lãi đầu tư</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Rental Income -->
                            <div class="transaction-card" onclick="openTransactionWizard('income', 'rental_income')">
                                <div class="card-icon">🏠</div>
                                <div class="card-content">
                                    <h4>Thu cho thuê</h4>
                                    <p>Cho thuê nhà, văn phòng, thiết bị</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Other Income -->
                            <div class="transaction-card" onclick="openTransactionWizard('income', 'other_income')">
                                <div class="card-icon">➕</div>
                                <div class="card-content">
                                    <h4>Thu khác</h4>
                                    <p>Các khoản thu khác</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>
                        </div>
                    </div>

                    <!-- Expense Section -->
                    <div class="content-section" style="background: linear-gradient(135deg, #ffeaea 0%, #fff0f0 100%); border: 2px solid #e74c3c; border-radius: 12px;">
                        <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                            <span class="material-icons" style="color: #e74c3c; font-size: 2rem; margin-right: 0.5rem;">trending_down</span>
                            <h3 style="color: #e74c3c; margin: 0;">📉 Chi phí</h3>
                        </div>

                        <div class="transaction-categories">
                            <!-- Salary Expense -->
                            <div class="transaction-card" onclick="openTransactionWizard('expense', 'salary_expense')">
                                <div class="card-icon">👥</div>
                                <div class="card-content">
                                    <h4>Chi lương</h4>
                                    <p>Lương, thưởng, phụ cấp nhân viên</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Tax Payment -->
                            <div class="transaction-card" onclick="openTransactionWizard('expense', 'tax_payment')">
                                <div class="card-icon">🏛️</div>
                                <div class="card-content">
                                    <h4>Chi thuế</h4>
                                    <p>GTGT, TNDN, TNCN, môn bài</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Material Expense -->
                            <div class="transaction-card" onclick="openTransactionWizard('expense', 'material_expense')">
                                <div class="card-icon">📦</div>
                                <div class="card-content">
                                    <h4>Chi nguyên vật liệu</h4>
                                    <p>Nguyên liệu, hàng hóa, vật tư</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Office Expense -->
                            <div class="transaction-card" onclick="openTransactionWizard('expense', 'office_expense')">
                                <div class="card-icon">🏢</div>
                                <div class="card-content">
                                    <h4>Chi văn phòng</h4>
                                    <p>Điện nước, internet, văn phòng phẩm</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>

                            <!-- Other Expense -->
                            <div class="transaction-card" onclick="openTransactionWizard('expense', 'other_expense')">
                                <div class="card-icon">➖</div>
                                <div class="card-content">
                                    <h4>Chi khác</h4>
                                    <p>Các khoản chi khác</p>
                                </div>
                                <span class="material-icons card-arrow">arrow_forward_ios</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction History -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <!-- Income History -->
                    <div class="content-section">
                        <h3 style="color: #27ae60;">📈 Lịch sử thu nhập</h3>
                        <div id="incomeHistory" class="transaction-list">
                            <p style="text-align: center; color: #666; padding: 2rem;">
                                Chưa có giao dịch thu nào
                            </p>
                        </div>
                    </div>

                    <!-- Expense History -->
                    <div class="content-section">
                        <h3 style="color: #e74c3c;">📉 Lịch sử chi phí</h3>
                        <div id="expenseHistory" class="transaction-list">
                            <p style="text-align: center; color: #666; padding: 2rem;">
                                Chưa có giao dịch chi nào
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports Section -->
            <div id="reports" class="section hidden">
                <div class="header">
                    <h1>Báo cáo tài chính</h1>
                </div>

                <div class="content-section">
                    <h3>Báo cáo tháng này</h3>
                    <div id="monthlyReport">
                        <p>Báo cáo sẽ được tạo tự động dựa trên dữ liệu giao dịch</p>
                    </div>
                </div>
            </div>

            <!-- Tax Section -->
            <div id="tax" class="section hidden">
                <div class="header">
                    <h1>Quản lý thuế</h1>
                </div>

                <div class="content-section">
                    <h3>Tính toán thuế GTGT</h3>
                    <div class="transaction-form">
                        <div class="form-group">
                            <label>Doanh thu (chưa thuế)</label>
                            <input type="number" id="revenue" placeholder="0" min="0">
                        </div>
                        <div class="form-group">
                            <label>Thuế suất GTGT</label>
                            <select id="vatRate">
                                <option value="0">0% (Xuất khẩu, Y tế, Giáo dục)</option>
                                <option value="5">5% (Nước sạch, một số dịch vụ)</option>
                                <option value="10" selected>10% (Hàng hóa, dịch vụ thông thường)</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn" onclick="calculateVAT()">Tính thuế GTGT</button>
                    <div id="vatResult" style="margin-top: 1rem;"></div>
                </div>
            </div>

            <!-- Google Sheets Section -->
            <div id="google-sheets" class="section hidden">
                <div class="header">
                    <h1>Đồng bộ Google Sheets</h1>
                </div>

                <div class="content-section">
                    <div id="sheets-disconnected" class="sheets-status">
                        <h3>Kết nối Google Sheets</h3>
                        <p style="margin-bottom: 1rem; color: #666;">
                            Kết nối với Google Sheets để tự động đồng bộ dữ liệu kế toán của bạn lên cloud.
                            Dữ liệu sẽ được tổ chức theo các sheet: Thu nhập, Chi phí, và Tổng hợp.
                        </p>
                        <button class="btn btn-success" onclick="connectGoogleSheets()">
                            <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">cloud_upload</span>
                            Kết nối Google Sheets
                        </button>
                    </div>

                    <div id="sheets-connected" class="sheets-status hidden">
                        <h3>✅ Đã kết nối Google Sheets</h3>
                        <p style="margin-bottom: 1rem; color: #27ae60;">
                            Dữ liệu của bạn đang được đồng bộ với Google Sheets.
                        </p>

                        <div style="display: flex; gap: 1rem; margin-bottom: 1rem; flex-wrap: wrap;">
                            <button class="btn" onclick="syncToSheets()">
                                <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">sync</span>
                                Đồng bộ ngay
                            </button>
                            <button class="btn" onclick="openGoogleSheet()" style="background: #34a853;">
                                <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">open_in_new</span>
                                Mở Google Sheet
                            </button>
                            <button class="btn" onclick="disconnectGoogleSheets()" style="background: #dc3545;">
                                <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">cloud_off</span>
                                Ngắt kết nối
                            </button>
                        </div>

                        <div id="sync-status" style="padding: 1rem; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #17a2b8;">
                            <strong>Trạng thái đồng bộ:</strong>
                            <div id="sync-info">Chưa đồng bộ lần nào</div>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h3>Cấu hình đồng bộ</h3>
                    <div class="transaction-form">
                        <div class="form-group">
                            <label>Tự động đồng bộ</label>
                            <select id="autoSync">
                                <option value="manual">Thủ công</option>
                                <option value="immediate">Ngay lập tức khi có giao dịch mới</option>
                                <option value="daily">Hàng ngày</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Tên Google Sheet</label>
                            <input type="text" id="sheetName" placeholder="Kế toán TT133" value="Kế toán TT133">
                        </div>
                    </div>
                    <button class="btn" onclick="updateSyncSettings()">Cập nhật cài đặt</button>
                </div>

                <div class="content-section">
                    <h3>Hướng dẫn sử dụng</h3>
                    <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px; border-left: 4px solid #2196f3;">
                        <h4>📋 Cách thức hoạt động:</h4>
                        <ol style="margin: 0.5rem 0; padding-left: 1.5rem;">
                            <li>Nhấn "Kết nối Google Sheets" để đăng nhập Google</li>
                            <li>Ứng dụng sẽ tạo một Google Sheet mới với 3 tab:
                                <ul style="margin: 0.5rem 0; padding-left: 1rem;">
                                    <li><strong>Thu nhập:</strong> Tất cả giao dịch thu</li>
                                    <li><strong>Chi phí:</strong> Tất cả giao dịch chi</li>
                                    <li><strong>Tổng hợp:</strong> Báo cáo tổng hợp theo tháng</li>
                                </ul>
                            </li>
                            <li>Dữ liệu sẽ được đồng bộ theo cài đặt của bạn</li>
                            <li>Bạn có thể mở Google Sheet để xem và chỉnh sửa</li>
                        </ol>

                        <h4 style="margin-top: 1rem;">🔒 Bảo mật:</h4>
                        <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                            <li>Chỉ bạn mới có quyền truy cập Google Sheet</li>
                            <li>Ứng dụng chỉ đọc/ghi dữ liệu, không truy cập thông tin khác</li>
                            <li>Bạn có thể ngắt kết nối bất cứ lúc nào</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Google APIs -->
    <script src="https://apis.google.com/js/api.js"></script>
    <script src="standalone-app.js"></script>
</body>
</html>
