<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Kế toán TT 133 - Fixed Container Option 3 (Min-Max Height)</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 95%;
            max-width: 1200px;
            height: 80vh;
            min-height: 600px;
            max-height: 900px;
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: 1fr;
        }

        .sidebar {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            grid-column: 1;
            grid-row: 1;
        }

        .main-content {
            padding: 2rem;
            background: #f8f9fa;
            grid-column: 2;
            grid-row: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: #3498db;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid;
        }

        .stat-card.income { border-left-color: #27ae60; }
        .stat-card.expense { border-left-color: #e74c3c; }
        .stat-card.profit { border-left-color: #3498db; }
        .stat-card.transactions { border-left-color: #f39c12; }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .content-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .hidden {
            display: none;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            body {
                padding: 0;
            }

            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
                width: 100%;
                height: 100vh;
                min-height: 100vh;
                max-height: 100vh;
                border-radius: 0;
            }

            .sidebar {
                grid-column: 1;
                grid-row: 1;
                padding: 1rem;
            }

            .main-content {
                grid-column: 1;
                grid-row: 2;
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <div class="logo-icon">
                    <span class="material-icons">trending_up</span>
                </div>
                <div>
                    <h3>Kế toán TT 133</h3>
                    <small>Min-Max Height</small>
                </div>
            </div>

            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                            <span class="material-icons">dashboard</span>
                            Tổng quan
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('transactions')">
                            <span class="material-icons">receipt</span>
                            Giao dịch
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('reports')">
                            <span class="material-icons">assessment</span>
                            Báo cáo
                        </a>
                    </li>
                </ul>
            </nav>

            <div style="margin-top: auto; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1);">
                <small>© 2024 App Kế toán TT 133</small>
                <br>
                <small style="opacity: 0.7;">Min-Max Height Layout</small>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section">
                <div class="header">
                    <h1>Dashboard - Min-Max Height</h1>
                    <button class="btn">
                        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">add</span>
                        Thêm giao dịch
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-card income">
                        <div class="stat-value">0 ₫</div>
                        <div class="stat-label">Tổng thu tháng này</div>
                    </div>
                    <div class="stat-card expense">
                        <div class="stat-value">0 ₫</div>
                        <div class="stat-label">Tổng chi tháng này</div>
                    </div>
                    <div class="stat-card profit">
                        <div class="stat-value">0 ₫</div>
                        <div class="stat-label">Lợi nhuận ròng</div>
                    </div>
                    <div class="stat-card transactions">
                        <div class="stat-value">0</div>
                        <div class="stat-label">Số giao dịch</div>
                    </div>
                </div>

                <div class="content-section">
                    <h3>✨ Ưu điểm của Min-Max Height Layout:</h3>
                    <ul style="margin-top: 1rem; line-height: 1.6;">
                        <li>✅ Container có height: 80vh cố định</li>
                        <li>✅ min-height: 600px (không quá nhỏ)</li>
                        <li>✅ max-height: 900px (không quá lớn)</li>
                        <li>✅ Responsive tốt với constraints</li>
                        <li>✅ Giữ được cảm giác "card" đẹp mắt</li>
                        <li>✅ Hoàn toàn không thay đổi kích thước</li>
                    </ul>
                </div>
            </div>

            <!-- Transactions Section -->
            <div id="transactions" class="section hidden">
                <div class="header">
                    <h1>Giao dịch - Min-Max Height</h1>
                </div>
                <div class="content-section">
                    <p>Nội dung giao dịch ngắn - Container vẫn giữ nguyên kích thước</p>
                    <div style="height: 200px; background: linear-gradient(45deg, #e8f5e8, #f0f9f0); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 1rem 0;">
                        <p style="color: #666;">Nội dung ngắn</p>
                    </div>
                </div>
            </div>

            <!-- Reports Section -->
            <div id="reports" class="section hidden">
                <div class="header">
                    <h1>Báo cáo - Min-Max Height</h1>
                </div>
                <div class="content-section">
                    <p>Nội dung báo cáo dài - Container cố định, có scroll</p>
                    <div style="height: 600px; background: linear-gradient(45deg, #ffeaea, #fff0f0); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 1rem 0;">
                        <p style="color: #666;">Nội dung dài - Test scroll</p>
                    </div>
                    <div style="height: 400px; background: linear-gradient(45deg, #e8f5e8, #f0f9f0); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 1rem 0;">
                        <p style="color: #666;">Nội dung tiếp theo</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');

            // Update navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            event.target.closest('.nav-link').classList.add('active');
        }
    </script>
</body>
</html>
